# Galaxy Queue Blob Implementation

## Overview
This implementation adds blob storage support to the `galaxy_queues` table to handle large payload data that exceeds the JSON column size limits. When content is too long, it's automatically stored in a blob format while maintaining backward compatibility.

## Changes Made

### 1. Database Migration
- **File**: `database/migrations/2024_12_16_000000_add_payload_blob_to_galaxy_queues_table.php`
- **Purpose**: Adds `payload_blob` LONGTEXT column to store large payloads
- **Action**: Run `php artisan migrate` to apply this change

### 2. GalaxyQueue Model Updates
- **File**: `app/Model/v2/GalaxyQueue.php`
- **Changes**:
  - Added `payload_blob` to fillable array
  - Added `setPayloadData($data)` method to automatically choose storage method
  - Added `getPayloadData()` method to retrieve data from appropriate column
  - Updated `run()` method to use `getPayloadData()`
  - Updated `SendForProcessing()` method to use blob storage when needed

### 3. Handler Updates
Updated all queue handlers to use the new blob-aware methods:

#### BulkLetterEmailProcessor
- **File**: `app/Queues/Handlers/BulkLetterEmailProcessor.php`
- **Changes**: Updated `updateQueuePayload()` to use `getPayloadData()` and `setPayloadData()`

#### BulkEmailProcessor
- **File**: `app/Queues/Handlers/BulkEmailProcessor.php`
- **Changes**: Updated `updateQueuePayload()` to use `getPayloadData()` and `setPayloadData()`

#### BulkStaffEmailProcessor
- **File**: `app/Queues/Handlers/BulkStaffEmailProcessor.php`
- **Changes**: Updated `updateQueuePayload()` to use `getPayloadData()` and `setPayloadData()`

### 4. Repository Updates
- **File**: `app/Repositories/OnboardRepository.php`
- **Changes**: Updated `getGlobalQueueDetail()` method to use `getPayloadData()` for accessing payload information

## How It Works

### Automatic Storage Selection
- **Small payloads** (≤ 64KB): Stored in `payload` JSON column
- **Large payloads** (> 64KB): Stored in `payload_blob` LONGTEXT column
- The `payload` column contains metadata: `{'_use_blob': true, '_blob_size': <size>}`

### Backward Compatibility
- Existing code continues to work without changes
- The `getPayloadData()` method automatically returns data from the correct column
- Old records with data in `payload` column continue to work normally

### Usage in studentIssueLetterEmail Function
When `studentIssueLetterEmail` is called:
1. Letter payload is prepared using `prepareLetterPayload()`
2. `GalaxyQueue::SendForProcessing()` automatically detects if payload is large
3. If large, stores in `payload_blob`; if small, stores in `payload`
4. Queue handlers retrieve data using `getPayloadData()` regardless of storage location

## Testing
- **File**: `tests/Unit/GalaxyQueueBlobTest.php`
- **Purpose**: Verifies blob storage functionality works correctly
- **Run**: `php artisan test tests/Unit/GalaxyQueueBlobTest.php`

## Benefits
1. **No size limits**: Can handle very large letter content
2. **Automatic**: No code changes needed in existing functionality
3. **Backward compatible**: Existing data continues to work
4. **Efficient**: Small payloads still use fast JSON storage
5. **Transparent**: Handlers don't need to know about storage method

## Migration Steps
1. Run the migration: `php artisan migrate`
2. Deploy the updated code
3. Test with large letter content
4. Monitor queue processing to ensure everything works correctly

## Notes
- The 64KB threshold can be adjusted in the `setPayloadData()` method if needed
- The `payload` column is kept for backward compatibility and small payloads
- Large payloads are JSON-encoded and stored as text in the blob column
