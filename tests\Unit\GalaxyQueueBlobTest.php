<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Model\v2\GalaxyQueue;
use Illuminate\Foundation\Testing\RefreshDatabase;

class GalaxyQueueBlobTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_stores_small_payload_in_json_column()
    {
        $queue = new GalaxyQueue();
        $smallPayload = ['test' => 'data', 'small' => 'payload'];
        
        $queue->setPayloadData($smallPayload);
        
        $this->assertEquals($smallPayload, $queue->payload);
        $this->assertNull($queue->payload_blob);
    }

    /** @test */
    public function it_stores_large_payload_in_blob_column()
    {
        $queue = new GalaxyQueue();
        
        // Create a large payload (over 64KB)
        $largePayload = [
            'letter_content' => str_repeat('This is a very long letter content that will exceed the JSON size limit. ', 1000),
            'student_id' => '1,2,3,4,5',
            'other_data' => str_repeat('More data to make it even larger. ', 500)
        ];
        
        $queue->setPayloadData($largePayload);
        
        $this->assertNotNull($queue->payload_blob);
        $this->assertTrue(isset($queue->payload['_use_blob']));
        $this->assertTrue($queue->payload['_use_blob']);
        $this->assertGreaterThan(65536, $queue->payload['_blob_size']);
    }

    /** @test */
    public function it_retrieves_small_payload_correctly()
    {
        $queue = new GalaxyQueue();
        $smallPayload = ['test' => 'data', 'small' => 'payload'];
        
        $queue->setPayloadData($smallPayload);
        $retrievedPayload = $queue->getPayloadData();
        
        $this->assertEquals($smallPayload, $retrievedPayload);
    }

    /** @test */
    public function it_retrieves_large_payload_correctly()
    {
        $queue = new GalaxyQueue();
        
        // Create a large payload (over 64KB)
        $largePayload = [
            'letter_content' => str_repeat('This is a very long letter content that will exceed the JSON size limit. ', 1000),
            'student_id' => '1,2,3,4,5',
            'other_data' => str_repeat('More data to make it even larger. ', 500)
        ];
        
        $queue->setPayloadData($largePayload);
        $retrievedPayload = $queue->getPayloadData();
        
        $this->assertEquals($largePayload, $retrievedPayload);
    }
}
