<?php

namespace App\Queues\Handlers;

use App\Traits\LetterGenerateAndSendTrait;
use Illuminate\Http\Request;
use Support\Contracts\ArrayableEntity;
use App\Contracts\QueueHandlerContract;
use App\DTO\studentProfile\DeleteCompletedDTO;
use App\Exceptions\ApplicationException;
use App\Services\StudentProfileCommonService;
use App\Repositories\StudentProfileCommonRepository;
use App\Http\Requests\FormValidation\StudentProfile\SendQueueLetterEmailRequest;
use App\Model\v2\GalaxyQueue;
use App\Helpers\Helpers;
use App\Model\v2\Student;
use App\Model\v2\StudentCommunicationLog;
use App\Repositories\StudentRepository;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class BulkLetterEmailProcessor implements QueueHandlerContract
{
    use LetterGenerateAndSendTrait;

    protected $studentProfileCommonService;
    protected $studentProfileCommonRepository;
    protected $communicationLog;

    public function __construct(
        StudentProfileCommonService $studentProfileCommonService,
        StudentProfileCommonRepository $studentProfileCommonRepository,
        StudentCommunicationLog $communicationLog
    ) {
        $this->studentProfileCommonService = $studentProfileCommonService;
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
        $this->communicationLog = new StudentRepository($communicationLog);
        ini_set('memory_limit', '-1');
    }


    public function handle(ArrayableEntity $payload, ArrayableEntity|array $config, int $queueId): void
    {
        try {
            $result = $this->sendLetterWithMailToStudentTrait($payload);
            Log::info('global queue result', [$result]);
            $this->updateQueuePayload($queueId, $result );
            if (isset($result['failed']) && !empty($result['failed'])) {
                 throw new ApplicationException(@$result['failed_student_id_with_reason'] ?? 'Something went wrong.');
            }
            $this->deleteCompletedIfAllProcessed(DeleteCompletedDTO::LazyFromArray($payload->toArray()));
        } catch (\Exception $e) {
            Log::info('global queue failed', [$e->getMessage(), $e->getTraceAsString()]);
            throw new ApplicationException($e->getMessage());
        } catch (\Throwable $e) {
            Log::info('global queue failed', [$e->getMessage(), $e->getTraceAsString()]);
            throw new ApplicationException($e->getMessage());
        }
    }

    public function payload($payload = []): ArrayableEntity
    {
        $request = new SendQueueLetterEmailRequest($payload);
        return $request->DTO();
    }

    public function config($config = []): ArrayableEntity|array
    {
        return $config ?? [];
    }

    public static function deleteCompletedIfAllProcessed(DeleteCompletedDTO $payload)
    {
        $tenant = tenant();
        if ($tenant) {
            $group = $payload->groupName;
            $pendingOrProcessingCount = GalaxyQueue::where('group', $group)->whereIn('status', [GalaxyQueue::STATUS_PENDING, GalaxyQueue::STATUS_FAILED])->count();
            if ($pendingOrProcessingCount == 0) {
                $filePath = Config::get('constants.uploadFilePath.SendEmailTemp');
                $destinationPath = Helpers::changeRootPath($filePath, $group, $payload->college_id);
                $folderPath = public_path($destinationPath['view']);
                if (File::exists($folderPath)) {
                    File::deleteDirectory($folderPath);
                }
            }
        }
    }
    private function updateQueuePayload($queueId,$result): void
    {
        $tenant = tenant();
        if ($tenant) {
            $failedDetails = @$result['failed'];
            $queue =  GalaxyQueue::find($queueId);

            // Get payload data using the new method
            $currentPayload = $queue->getPayloadData();
            $allIds = explode(',', $currentPayload['allStudentId']);
            $successIds = array_diff($allIds, $failedDetails);

            $newPayload = (array) $currentPayload;  // Ensure it's an array
            $newPayload['failedEmailStudentId'] = implode(',', $failedDetails);
            $newPayload['failedStudentIdWithReason'] = @$result['failed_student_id_with_reason'];
            $newPayload['successEmailStudentId'] = implode(',', $successIds);
            $newPayload['student_id'] = implode(',', $failedDetails);

            $students = Student::whereIn('id', $successIds)->get();
            $studentNames = $students->map(function($student) {
                return $student->first_name . ' ' . $student->family_name;
            })->toArray();
            $studentNamesString = implode(', ', $studentNames);
            $response['success'] = ($studentNamesString) ? "Email Successfully sent to ".$studentNamesString:"-";
            $response['fail'] = @$result['failed_student_id_with_reason'];

            // Use the new setPayloadData method to handle blob storage
            $queue->setPayloadData($newPayload);
            $queue->response = json_encode($response);
            $queue->queue_attempt = $queue->queue_attempt + 1;
            $queue->status = GalaxyQueue::STATUS_PENDING;
            $queue->save();
        }
    }
}
