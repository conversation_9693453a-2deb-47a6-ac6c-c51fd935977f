<?php namespace App\Repositories;

use App\Helpers\Helpers;
use App\Model\v2\Student;
use Illuminate\Database\Eloquent\Model;
use App\Model\v2\SetupSection;
use App\Traits\CommonTrait;
use App\Model\v2\OfferDocumentChecklist;
use App\Model\v2\AgentDocumentChecklist;
use App\Model\v2\OfferTrackingStatus;
use App\Model\v2\Checklist;
use App\Model\v2\CampusVenue;
use App\Model\v2\Classroom;
use Illuminate\Support\Facades\DB;
use App\Model\v2\GteDocumentMaster;
use App\Model\v2\CourseType;
use App\Model\v2\Country;
use App\Model\v2\ServicesFee;
use App\Model\v2\ServiceName;
use App\Model\v2\Language;
use App\Model\v2\AgentStatus;
use App\Model\v2\GradingType;
use App\Model\v2\ResultGrade;
use App\Model\v2\OSHC;
use App\Model\v2\OSHCProvider;
use App\Model\v2\InterventionStrategy;
use App\Model\v2\InterventionType;
use App\Model\v2\CertificateIdFormate;
use App\Model\v2\AssessmentDueDate;
use App\Model\v2\AgentEmailTemplateSetting;
use App\Model\v2\GalaxyQueue;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

class OnboardRepository {

    use CommonTrait;
    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function with($relations){
        return $this->model->with($relations);
    }
    public function create(array $data){
        return $this->model->create($data);
    }
    public function modify(array $data, array $where){
        return $this->model->where($where)->update($data);
    }
    public function update(array $data, $id){
        $record = $this->model->find($id);
        return $record->update($data);
    }
    public function delete($id){
        return $this->model->destroy($id);
    }
    public function show($id){
        return $this->model->findOrFail($id);
    }
    public function withId($relations,$id){
        return $this->model->with($relations)->find($id);
    }
    public function selectdata( $fields='*'){
        $selectdata = $this->model->select($fields);
        return $selectdata;
    }
    public function getData(array $where){
        return $this->model->where($where)->get()->toArray();
    }
    public function getWhere($whereArr, $fields='*') {
        return $this->model->where($whereArr)->select($fields)->get();
    }
    public function getValue($whereArr, $field) {
        return $this->model->where($whereArr)->get()->value($field);
    }
    public function Where($whereArr) {
        return $this->model->where($whereArr);
    }

    public function getOfferLetterData($request, $countOnly=false){

        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];
        //$customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];

        $columnArr = array(
             'rdc.id',
             'rdc.document_name',
             DB::raw("(CASE WHEN rdc.document_type = 1 THEN 'In_Application' ELSE (CASE WHEN rdc.document_type = '2' THEN 'Post_Application' ELSE 'N/A' END) END) as document_type"),
             'rdc.student_origin',
             'rdc.is_compulsory',
             'rdc.is_active',
        );

        $columns = array(
            'document_name' => 'rdc.document_name',
            'document_type' => DB::raw("(CASE WHEN rdc.document_type = 1 THEN 'In_Application' ELSE (CASE WHEN rdc.document_type = '2' THEN 'Post_Application' ELSE 'N/A' END) END) as document_type")
        );

        $query = OfferDocumentChecklist::from('rto_offer_document_checklist as rdc')
                    ->where('college_id', '=', $college_id)
                    ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getSectionData($request, $countOnly=false){

        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = array(
            'rss.id',
            'rsso.id as section_id',
            'rsso.name as section',
            'rss.type as type_id',
            DB::raw("(CASE WHEN rc.course_name != '' THEN rc.course_name ELSE rsst.type_name END) as type"),
            'rss.value',
            'rss.is_default as status'
        );

        $columns = array(
            'id'        => 'rss.id',
            'section'   => 'rsso.name as section',
            'type'      => DB::raw("(CASE WHEN rc.course_name != '' THEN rc.course_name ELSE rsst.type_name END) as type"),
            'value'     => 'rss.value as value',
            'status'    => 'rss.is_default as status',
        );

        $query = SetupSection::from('rto_setup_section as rss')
                ->leftjoin('rto_setup_section_option as rsso', 'rsso.id', '=', 'rss.section')
                ->leftjoin('rto_setup_section_type as rsst', function($join) {
                    $join->on('rsst.id', '=', 'rss.type')->where('rss.section', '!=', 1);
                })
                ->leftjoin('rto_courses as rc', function($join) {
                    $join->on('rc.id', '=', 'rss.type')->where('rss.section', '=', 1);
                })
                ->where('rss.college_id', '=', $college_id)
                ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getAgentDocumentsData($request, $countOnly=false){

        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = array(
             'rdc.id',
             'rdc.document_name',
             'rdc.is_compulsory',
             'rdc.is_active'
        );
        $columns = array(
            'document_name' => 'rdc.document_name'
        );
        $query = AgentDocumentChecklist::from('rto_agent_document_checklist as rdc')->where('college_id', '=', $college_id)->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getOfferTrackingData($request, $countOnly=false) {

        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = array(
             'rts.id',
             'rts.status_name',
             'rts.agent_view',
             'rts.is_allowagent',
        );
        $columns = array(
            'status_name'   => 'rts.status_name',
            'agent_view'    => 'rts.agent_view'
        );
        $query = OfferTrackingStatus::from('rto_offer_tracking_status as rts')->where('college_id', '=', $college_id)->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getCustomCheckListData($request, $countOnly=false){

        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = array(
             'rc.id',
             'rc.mandatory',
             'rc.checklist_name',
             'rc.description',
             'rc.checklist_for',
        );
        $columns = array(
            'checklist_name'    => 'rc.checklist_name',
            'checklist_for'     => 'rc.checklist_for',
            'description'       => 'rc.description',
            'mandatory'         => 'rc.mandatory'
        );
        $query = Checklist::from('rto_checklist as rc')->where('college_id', '=', $college_id)->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getVenueData($request, $countOnly=false){

        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = array(
             'rv.id',
             'rv.venue_code',
             'rv.venue_name',
             'rv.street_name',
             'rv.contact_no',
             'rv.campus_id',
             'rv.abn',
             'rv.set_active',
             'rv.status_default'
        );

        $columns = array(
            'venue_code'    => 'rv.venue_code',
            'venue_name'    => 'rv.venue_name',
            'street_name'   => 'rv.street_name',
            'contact_no'    => 'rv.contact_no',
            'abn'           => 'rv.abn'
        );
        $query = CampusVenue::from('rto_venue as rv')->where('college_id', '=', $college_id)->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getRoomData($request, $countOnly=false){

        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = array(
             'rc.id',
             'rv.venue_code',
             'rc.room_id',
             'rc.room_name',
             'rc.max_capacity',
             'rc.status',
        );

        $columns = array(
            'venue_code'    => 'rv.venue_code',
            'room_id'       =>  'rc.room_id',
            'room_name'     => 'rc.room_name',
            'max_capacity'  => 'rc.max_capacity',
        );

        $query = Classroom::from('rto_classroom as rc')
                        ->leftjoin('rto_venue as rv', 'rv.id', '=', 'rc.venue_id')
                        ->where('rc.college_id', '=', $college_id)
                        ->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getCourseType($request, $countOnly=false){

        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'id',
            'title',
            'description',
            'status'
        );
        $columns = array(
            'title'         => 'title',
            'description'   => 'description'
        );
        $query = CourseType::from('rto_college_course_type')->whereIn('college_id', ['0', $college_id])->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getCountryList($request, $countOnly=false){

        // $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'id',
            'college_id',
            'name',
            'countrycode',
            'countrylevel',
            'nationality',
            'region',
            'absvalue',
            'status'
        );
        $columns = array(
            'name'          => 'name',
            'countrycode'   => 'countrycode',
            'nationality'   => 'nationality',
            'region'        => 'region',
            'absvalue'      => 'absvalue',
            'status'        => 'status'
         );
        $query = Country::from('rto_country')->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function gteDocumentData($request, $countOnly=false){
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'id',
            'college_id',
            'document_name'
        );
        $columns = array(
            'id'            => 'id',
            'college_id'    => 'college_id',
            'document_name' => 'document_name'
        );
        $query = GteDocumentMaster::from('rto_gte_document_master')
                                    ->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function gteAddedServicesFeeData($request, $countOnly=false){

        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'rsf.id',
            'rsf.college_id',
            'rsn.service_name',
            'rsf.service_fee'
        );
        $columns = array(
            'id'            => 'rsf.id',
            'college_id'    => 'rsf.college_id',
            'service_name'  => 'rsn.service_name',
            'service_fee'   => 'rsf.service_fee',
        );
        $query = ServicesFee::from('rto_services_fee as rsf')
                            ->join('rto_service_name as rsn','rsn.id','rsf.service_name')
                            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function gteServiceName($request, $countOnly=false){
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'id',
            'service_name'
        );
        $columns = array(
            'id'            => 'id',
            'service_name'  => 'service_name'
        );
        $query = ServiceName::from('rto_service_name')->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);
        return $result;
    }

    public function getLanguageData($request, $countOnly=false){
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'id',
            'name',
            'abs_value'
        );
        $columns = array(
            'name'          => 'name',
            'abs_value'     =>'abs_value'
        );
        $query = Language::from('rto_language')->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);
        return $result;
    }

    public function getAgentStatusData($request, $countOnly=false){

        $collegeId = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'id',
            'status_type',
            'duration',
            'publish'
        );
        $columns = array(
            'status_type'   => 'status_type',
            'duration'      =>'duration',
            'publish'       =>'publish'
        );
        $query = AgentStatus::from('rto_agent_status')
           ->where('college_id', $collegeId)->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);
        return $result;
    }

    public function getResultGradeData($request, $countOnly=false){

        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'rsg.id',
            'rgt.grading_name as grading_name',
            'rsg.use_marks',
            'rsg.force_matching_outcome_code',
            'rsg.maximum_marks',
            'rsg.marks',
            'rsg.trascript_display',
            'rsg.grade',
            'rsg.competency_calculation',
            'rsg.final_outcome_code',
            'rsg.unit_of_study_status_code',
        );
        $columns = array(
            'grading_name'                  =>'rgt.grading_name as grading_name',
            'use_marks'                     =>'rsg.use_marks',
            'force_matching_outcome_code'   =>'rsg.force_matching_outcome_code',
            'maximum_marks'                 =>'rsg.maximum_marks',
            'marks'                         =>'rsg.marks',
            'trascript_display'             =>'rsg.trascript_display',
            'grade'                         =>'rsg.grade',
            'competency_calculation'        =>'rsg.competency_calculation',
            'final_outcome_code'            =>'rsg.final_outcome_code',
            'unit_of_study_status_code'     =>'rsg.unit_of_study_status_code'
        );

        $query = ResultGrade::from('rto_result_grade as rsg')
            ->leftjoin('rto_grading_type as rgt', 'rsg.grading_type', '=', 'rgt.id')
            ->where('rsg.college_id', $college_id)
            ->select($columnArr);
        $arrFinalOutcome = Config::get('constants.arrFinalOutcome');


        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPagination($query, $post, $countOnly);
        if(!$countOnly) {
            foreach ($result as $k => $row) {
                $result[$k]['final_outcome_code'] = $arrFinalOutcome[$row['final_outcome_code']];
            }
        }
        return $result;
    }

    public function getOSHCProviderName($request, $countOnly=false){
        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'id',
            'provider_name'
        );
        $columns = array(
            'id'            => 'id',
            'provider_name'  => 'provider_name'
        );
        $query = OSHCProvider::from('rto_oshc_providers')->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);
        return $result;
    }

    public function getOSHCInfoData($request, $countOnly=false){

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'roshc.id',
            'roshcp.provider_name as provider_name',
            'roshc.provide_id',
            'roshc.oshc_type',
            'roshc.duration',
            'roshc.valid_from',
            'roshc.valid_to',
            'roshc.oshc_fees',
            'roshc.is_active'
        );
        $columns = array(
            'provider_name'         =>'roshcp.provider_name as provider_name',
            'oshc_type'             =>'roshc.oshc_type',
            'duration'              =>'roshc.duration',
            'is_active'             =>'roshc.is_active'
        );

        $query = OSHC::from('rto_oshc as roshc')
            ->leftjoin('rto_oshc_providers as roshcp', 'roshcp.id', '=', 'roshc.provide_id')
            // ->where('roshc.college_id', $college_id)
            ->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);
        return $result;
    }

    public function updateOSHCActiveStatus($request){
        $college_id =Auth::user()->college_id;
        $isActive = ($request['status'] == 1) ? "0":"1";
        return  OSHC::where('college_id', '=', $college_id)->where('id', $request['id'])->update(['is_active' => $isActive]);
    }
    public function getInterventionStrategyData($request, $countOnly=false){

        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'ris.id',
            'ris.college_id',
            'rit.intervention_type',
            'ris.strategy'
        );
        $columns = array(
            'id'                    => 'ris.id',
            'college_id'            => 'ris.college_id',
            'intervention_type'  => 'rit.intervention_type',
            'strategy'              => 'ris.strategy',
        );
        $query = InterventionStrategy::from('rto_intervention_strategy as ris')
                            ->join('rto_intervention_type as rit','rit.id','ris.intervention_type_id')
                            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getCertificateIdFormateData($request, $countOnly=false){
        $college_id = $request->college_id;
        $post =($request->input()) ? $request->input() :[];
        $columnArr =array(
             'id',
             'type',
             'prefix',
             'auto_number',
             'suffix',
             'last_auto_increment_number'
        );
        $columns = array(
             'id'                           =>'id',
             'type'                         =>'type',
             'prefix'                       =>'prefix',
             'auto_number'                  =>'auto_number',
             'suffix'                       =>'suffix',
             'last_auto_increment_number'   =>'last_auto_increment_number'

        );
        $query = CertificateIdFormate :: from ('rto_certificate_id_formate')->select($columnArr);
        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);
        return $result;
    }
    public function getGlobalQueueData($request, $countOnly=false){

        $college_id = $request->college_id;
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = array(
            'id',
            'group',
            'label',
            'response',
            'status',
            'tenant_id',
            'queue_attempt',
            'started_at',
            'completed_at',
            'payload',
            DB::raw("COUNT(id) as groupCount"),
            DB::raw("MIN(started_at) as min_started_date"),
            DB::raw("MAX(completed_at) as min_completed_at"),
            DB::raw("MAX(id) as queueId"),
            DB::raw('(SELECT COUNT(CASE WHEN subquery.status = 3 THEN 1 END) FROM galaxy_queues AS subquery WHERE subquery.tenant_id = "' . tenant('id') . '" AND subquery.`group` = galaxy_queues.`group`) AS failed_count'),
            DB::raw('(SELECT COUNT(*) FROM galaxy_queues AS subquery WHERE subquery.tenant_id = "' . tenant('id') . '" AND subquery.`group` = galaxy_queues.`group`) AS total_count'),
            DB::raw('(SELECT COUNT(CASE WHEN subquery.status IN (2, 3) THEN 1 END) FROM galaxy_queues AS subquery WHERE subquery.tenant_id = "' . tenant('id') . '" AND subquery.`group` = galaxy_queues.`group`) AS completed_count'),
        );
        $columns = array(
            'group'         => 'group',
            'status'   => 'status'
        );

        if(isset($post['inProgress'])){
            $query = GalaxyQueue::where(['tenant_id'=>tenant('id')])->whereNotIn('status', [2,3])->select($columnArr)->orderBy('id','DESC')->groupBy('group');
        }else{
            $query = GalaxyQueue::where(['tenant_id'=>tenant('id')])->groupBy('group')->havingRaw('SUM(CASE WHEN status IN (2, 3) THEN 1 ELSE 0 END) = COUNT(*)')->select($columnArr)->orderBy('id','DESC')->groupBy('group');
        }

        $this->gridDataFilter($query, $post, $columns);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);
        if(!$countOnly) {
            foreach ($result as $k => $row) {


                $row['success'] = '';
                $row['fail'] = '';
                if(isset($row['response'])){
                    $response = json_decode($row['response'],true);
                    $row['success'] = $response['success'];
                    $row['fail'] = json_encode($response['fail']);
                }
                $row['batch'] = "Email";
                $row['sn'] = $k+1;
                $row['queue_sn'] = 'Queue #'.$row['id'];
                $row['min_started_date'] = Helpers::convertDateTimeToReadableFormat($row['min_started_date']);    // !empty($row['min_started_date']) ? date('d-m-Y h:i A',strtotime($row['min_started_date'])) :"-";
                $row['min_completed_at'] = Helpers::convertDateTimeToReadableFormat($row['min_completed_at']);    //!empty($row['min_completed_at']) ? date('d-m-Y h:i A',strtotime($row['min_completed_at'])) :"-";
                $startedAt = !empty($row['started_at']) ? date('d-m-Y',strtotime($row['started_at'])) :"-";
                $completedAt = !empty($row['completed_at']) ? date('d-m-Y',strtotime($row['completed_at'])) :"-";
                $row['started_at'] = $startedAt." / ".$completedAt;
                $row['process'] = $row['total_count'] > 0 ? number_format(($row['completed_count'] / $row['total_count']) * 100, 2) : 0;
                $row['status'] = GalaxyQueue::getStatusNameAttribute($row['status']);
                $result[$k] = (array) $row;

            }
        }
        return ['data'=>$result,'total'=>count($result)];
    }
    public function getCompletedPercentage($payload,$status){
            // Calculate the total number of students
            if($status == GalaxyQueue::STATUS_COMPLETE){
                return "100";

            }else{
                $totalCount = count( explode(',', $payload['allStudentId']));

                // Calculate the number of failed students
                if(isset($payload['failedEmailStudentId'])){
                    $failedCount = count(explode(',', $payload['failedEmailStudentId']));

                    $completedCount = $totalCount - $failedCount;
                        $completionPercentage = ($completedCount / $totalCount) * 100;

                   return number_format($completionPercentage, 2);
                }
                return "0.00";
            }


    }
    public function getGlobalQueueDetail($request){
            $columnArr = array(
                'id',
                'group',
                'label',
                'response',
                'config',
                'status',
                'tenant_id',
                'queue_attempt',
                'started_at',
                'completed_at',
                'payload',
                'payload_blob',
            );
          $result = GalaxyQueue::where('group',$request->input('id'))->select($columnArr)->orderBy('id','ASC')->get();
            foreach ($result as $k => $row) {
                $row['success'] = '';
                $row['fail'] = '';
                if(isset($row['response'])){
                        $response = json_decode($row['response'],true);
                        $row['success'] = $response['success'];
                        $row['fail'] = json_encode($response['fail']);
                    }
                    $row['groupNumber'] =  '#'.$k+1;
                    $studentNamesString = "";
                    $studentNames = [];

                    // Get payload data using the new method
                    $payloadData = $row->getPayloadData();
                    if(isset($payloadData['allStudentId'])){
                        $students = Student::whereIn('id', explode(',',$payloadData['allStudentId']))->get();
                        $studentNames = $students->map(function($student) {
                            return $student->first_name . ' ' . $student->family_name;
                        })->toArray();
                        $studentNamesString = implode(', ', $studentNames);
                    }

                    // $payloadData['failedStudentIdWithReason'] = [];
                    if(isset($payloadData['failedEmailStudentId'])){

                        $failedStudentIds = explode(',', $payloadData['failedEmailStudentId']);
                        $failStudents = Student::whereIn('id', $failedStudentIds)->get(['id', 'email', 'family_name','first_name']);

                        // Map the results to easily access them by student ID
                        $studentDetails = $failStudents->keyBy('id');

                        if(isset($payloadData['failedStudentIdWithReason'])) {
                            foreach ($payloadData['failedStudentIdWithReason'] as $index => $student) {
                                $studentId = $failedStudentIds[$index];

                                if (isset($studentDetails[$studentId])) {
                                    $payloadData['failedStudentIdWithReason'][$index]['email'] = $studentDetails[$studentId]->email;
                                    $payloadData['failedStudentIdWithReason'][$index]['name'] = $studentDetails[$studentId]->first_name . ' ' . $studentDetails[$studentId]->family_name;
                                }
                            }
                        }
                    }

                    if(!isset($payloadData['failedStudentIdWithReason'])){
                        $payloadData['failedStudentIdWithReason'] = [];
                    }
                    $row['successStudentsNames'] = [];
                    if(isset($payloadData['successEmailStudentId'])){

                        $row['successStudentsNames'] = $students->filter(function ($student) use ($payloadData) {
                            return in_array($student->id, explode(',',$payloadData['successEmailStudentId']));
                        })->map(function ($student) {
                            return [
                                'id' => $student->id,
                                'email' => $student->email,
                                'name' => $student->first_name . ' ' . $student->family_name,  // assuming name field exists, add more fields as needed
                            ];
                        })->values();
                    }
                    $row['allStudentsList'] = $studentNamesString;
                    $row['allStudentsListArray'] = $studentNames;
                    $row['payload'] = $payloadData; // Set the payload data for backward compatibility
                    //$startedAt = !empty($row['started_at']) ? date('d-m-Y h:i A',strtotime($row['started_at'])) :"-";
                    //$completedAt = !empty($row['completed_at']) ? date('d-m-Y h:i A',strtotime($row['completed_at'])) :"-";
                    $row['started_at'] = Helpers::convertDateTimeToReadableFormat($row['started_at']);      //$startedAt;
                    $row['completed_at'] = Helpers::convertDateTimeToReadableFormat($row['completed_at']);  //$completedAt;
                    $row['process'] = $this->getCompletedPercentage($payloadData,$row['status']);
                    $row['status'] = GalaxyQueue::getStatusNameAttribute($row['status']);

                    $result[$k] = (array) $row;
                }

         return $result;
        }

}