<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('galaxy_queues', function (Blueprint $table) {
            $table->id();
            $table->string('handler');
            $table->string('group');
            $table->json('payload');
            $table->longText('payload_blob')->nullable(); // New blob column for large payloads
            $table->json('config');
            $table->string('tenant_id');
            $table->integer('priority')->default(0);
            $table->integer('queue_attempt')->default(0);
            $table->integer('queue_tries')->default(0);
            $table->integer('status')->default(0);
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('galaxy_queues');
    }
};
