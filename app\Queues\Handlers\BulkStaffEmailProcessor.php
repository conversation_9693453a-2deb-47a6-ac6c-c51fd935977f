<?php

namespace App\Queues\Handlers;

use App\Http\Requests\SendCommunicationMailRequest;
use App\Model\v2\Staff;
use App\Traits\SendStaffEmailTrait;
use Illuminate\Http\Request;
use Support\Contracts\ArrayableEntity;
use App\Contracts\QueueHandlerContract;
use App\DTO\studentProfile\DeleteCompletedDTO;
use App\Exceptions\ApplicationException;
use App\Services\StudentProfileCommonService;
use App\Repositories\StudentProfileCommonRepository;
use App\Model\v2\GalaxyQueue;
use App\Helpers\Helpers;
use App\Model\v2\StudentCommunicationLog;
use App\Repositories\StudentRepository;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;

class BulkStaffEmailProcessor implements QueueHandlerContract
{
    use SendStaffEmailTrait;

    protected $studentProfileCommonService;
    protected $studentProfileCommonRepository;
    protected $communicationLog;

    public function __construct(
        StudentProfileCommonService $studentProfileCommonService,
        StudentProfileCommonRepository $studentProfileCommonRepository,
        StudentCommunicationLog $communicationLog
    ) {
        $this->studentProfileCommonService = $studentProfileCommonService;
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
        $this->communicationLog = new StudentRepository($communicationLog);
        ini_set('memory_limit', '-1');
    }


    public function handle(ArrayableEntity $payload, ArrayableEntity|array $config, int $queueId): void
    {
        try {
            $result = $this->sendMailToStaffTrait($payload);
            $this->updateQueuePayload($queueId, $result);

            if (isset($result['failed']) && !empty($result['failed'])) {
                throw new ApplicationException('Fail');
            }

            $this->deleteCompletedIfAllProcessed(DeleteCompletedDTO::LazyFromArray($payload->toArray()));
        } catch (\Exception $e) {
            throw new ApplicationException($e->getMessage());
        }
    }

    public function payload($payload = []): ArrayableEntity
    {
        $request = new SendCommunicationMailRequest($payload);
        return $request->DTO();
    }

    public function config($config = []): ArrayableEntity|array
    {
        return $config ?? [];
    }

    public static function deleteCompletedIfAllProcessed(DeleteCompletedDTO $payload)
    {
        $tenant = tenant();
        if ($tenant) {
            $group = $payload->groupName;
            $pendingOrProcessingCount = GalaxyQueue::where('group', $group)->whereIn('status', [GalaxyQueue::STATUS_PENDING, GalaxyQueue::STATUS_FAILED])->count();
            if ($pendingOrProcessingCount == 0) {
                $filePath = Config::get('constants.uploadFilePath.SendEmailTemp');
                $destinationPath = Helpers::changeRootPath($filePath, $group, $payload->college_id);
                $folderPath = public_path($destinationPath['view']);
                if (File::exists($folderPath)) {
                    File::deleteDirectory($folderPath);
                }
            }
        }
    }

    private function updateQueuePayload($queueId, $result): void
    {
        $tenant = tenant();
        if ($tenant) {
            $failedIds = $result['failed'];
            $queue = GalaxyQueue::find($queueId);

            // Get payload data using the new method
            $currentPayload = $queue->getPayloadData();
            $allIds = explode(',', $currentPayload['allStaffId']);
            $successIds = array_diff($allIds, $failedIds);

            $newPayload = array_merge(
                (array) $currentPayload, [
                    'successEmailStaffId'       => implode(',', $successIds),
                    'failedEmailStaffId'        => implode(',', $failedIds),
                    'failedStaffIdWithReason'   => $result['failed_staff_id_with_reason'],
                    'staff_id'                  => implode(',', $failedIds),
                ]
            );

            $staffNames = Staff::whereIn('id', $successIds)
                                ->get()
                                ->map(fn($staff) => "{$staff->first_name} {$staff->last_name}")
                                ->implode(', ');

            // Use the new setPayloadData method to handle blob storage
            $queue->setPayloadData($newPayload);
            $queue->response = json_encode([
                'success' => $staffNames ? "Email Successfully sent to {$staffNames}" : "-",
                'fail'    => $result['failed_staff_id_with_reason']
            ]);

            $queue->queue_attempt = $queue->queue_attempt + 1;
            $queue->status = GalaxyQueue::STATUS_PENDING;
            $queue->save();
        }
    }
}