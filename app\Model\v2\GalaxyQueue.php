<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use app\Contracts\QueueHandlerContract;
use Stancl\Tenancy\Database\Concerns\CentralConnection;


class GalaxyQueue extends Model
{
    use CentralConnection;
    protected $fillable = ['id', 'handler', 'group', 'payload', 'config', 'status', 'started_at', 'completed_at', 'tenant_id', 'priority', 'queue_attempt', 'queue_tries', 'response', 'label'];
    protected $table = 'galaxy_queues';
    const STATUS_PENDING = 0;
    const STATUS_PROCESSING = 1;
    const STATUS_COMPLETE = 2;
    const STATUS_FAILED = 3;
    const QUEUE_TRIES = 3;

    protected $casts = [
        'payload' => 'array',
        'config' => 'array',
        'id' => 'int',
    ];

    public function tenantName()
    {
        return $this->belongsTo(Tenant::class, 'tenant');
    }

    static function getStatusNameAttribute($status)
    {
        $statusMapping = [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_PROCESSING => 'Processing',
            self::STATUS_COMPLETE => 'Complete',
            self::STATUS_FAILED => 'Failed',
        ];
        return $statusMapping[$status] ?? '';
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING)
            ->whereNull('completed_at');
        //                     ->whereNull('started_at');
    }

    public function run(QueueHandlerContract $handler)
    {
        $this->markAsProcessing();
        info('queue processing', ['id' => $this->id]);
        try {
            $handler->handle($handler->payload($this->payload), $handler->config($this->config), $this->id);
            info('queue processed', ['id' => $this->id]);
            $this->markAsComplete();
        } catch (\Exception | \Throwable $e) {
            info('queue failed', ['id' => $this->id, 'reason' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            /* TODO: lets try to save the failed reason */
            $failedDetails = "Failed due to: " . $e->getMessage();
            $this->markAsFailed($failedDetails);
        }
    }

    protected function markAsProcessing(): void
    {
        $this->update([
            'status' => self::STATUS_PROCESSING,
            'started_at' => Carbon::now(),
        ]);
    }

    protected function markAsComplete()
    {

        $this->update([
            'status' => self::STATUS_COMPLETE,
            'completed_at' => Carbon::now(),
        ]);
    }


    protected function markAsFailed($failedDetails)
    {
        $response = ['success'=>'','fail'=>$failedDetails];
        if ($this->queue_attempt >= $this->queue_tries) {
            $this->update([
                'status' => self::STATUS_FAILED,
                'completed_at' => Carbon::now(),
                'response' => json_encode($response)
            ]);
        } else {
            $this->update([
                'queue_attempt' => $this->queue_attempt + 1,
                'status' => self::STATUS_PENDING,
                'response' => json_encode($response)
            ]);
        }
    }

    public static function SendForProcessing($handlerClass, $payloadDTO = [], $config = [], $groupName = null, $priority = null, $label = null): void
    {
        dd($payloadDTO);
        $tenant = tenant();
        if ($tenant) {
            // tenancy()->central(function () use ($tenant,$handlerClass,$groupName,$payloadDTO,$config,$priority) {
            GalaxyQueue::create([
                'handler'       => $handlerClass,
                'group'         => $groupName,
                'payload'       => $payloadDTO,
                'config'        => $config,
                'label'         => $label,
                'priority'      => $priority,
                'queue_tries'   => GalaxyQueue::QUEUE_TRIES,
                'tenant_id'     => $tenant->id,
                'status'        => GalaxyQueue::STATUS_PENDING,
            ]);
            // });
        }
    }
}

