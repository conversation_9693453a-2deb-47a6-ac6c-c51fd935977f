<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use app\Contracts\QueueHandlerContract;
use Stancl\Tenancy\Database\Concerns\CentralConnection;


class GalaxyQueue extends Model
{
    use CentralConnection;
    protected $fillable = ['id', 'handler', 'group', 'payload', 'payload_blob', 'config', 'status', 'started_at', 'completed_at', 'tenant_id', 'priority', 'queue_attempt', 'queue_tries', 'response', 'label'];
    protected $table = 'galaxy_queues';
    const STATUS_PENDING = 0;
    const STATUS_PROCESSING = 1;
    const STATUS_COMPLETE = 2;
    const STATUS_FAILED = 3;
    const QUEUE_TRIES = 3;

    protected $casts = [
        'payload' => 'array',
        'config' => 'array',
        'id' => 'int',
    ];

    public function tenantName()
    {
        return $this->belongsTo(Tenant::class, 'tenant');
    }

    static function getStatusNameAttribute($status)
    {
        $statusMapping = [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_PROCESSING => 'Processing',
            self::STATUS_COMPLETE => 'Complete',
            self::STATUS_FAILED => 'Failed',
        ];
        return $statusMapping[$status] ?? '';
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING)
            ->whereNull('completed_at');
        //                     ->whereNull('started_at');
    }

    /**
     * Get the payload data, preferring blob data if available
     */
    public function getPayloadData()
    {
        if (!empty($this->payload_blob)) {
            return json_decode($this->payload_blob, true);
        }
        return $this->payload;
    }

    /**
     * Set the payload data, using blob if content is too large
     */
    public function setPayloadData($data)
    {
        $jsonData = json_encode($data);
        $jsonSize = strlen($jsonData);

        // If payload is larger than 64KB, use blob storage
        if ($jsonSize > 65536) {
            $this->payload_blob = $jsonData;
            $this->payload = ['_use_blob' => true, '_blob_size' => $jsonSize];
        } else {
            $this->payload = $data;
            $this->payload_blob = null;
        }
    }

    public function run(QueueHandlerContract $handler)
    {
        $this->markAsProcessing();
        info('queue processing', ['id' => $this->id]);
        try {
            $payloadData = $this->getPayloadData();
            $handler->handle($handler->payload($payloadData), $handler->config($this->config), $this->id);
            info('queue processed', ['id' => $this->id]);
            $this->markAsComplete();
        } catch (\Exception | \Throwable $e) {
            info('queue failed', ['id' => $this->id, 'reason' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            /* TODO: lets try to save the failed reason */
            $failedDetails = "Failed due to: " . $e->getMessage();
            $this->markAsFailed($failedDetails);
        }
    }

    protected function markAsProcessing(): void
    {
        $this->update([
            'status' => self::STATUS_PROCESSING,
            'started_at' => Carbon::now(),
        ]);
    }

    protected function markAsComplete()
    {

        $this->update([
            'status' => self::STATUS_COMPLETE,
            'completed_at' => Carbon::now(),
        ]);
    }


    protected function markAsFailed($failedDetails)
    {
        $response = ['success'=>'','fail'=>$failedDetails];
        if ($this->queue_attempt >= $this->queue_tries) {
            $this->update([
                'status' => self::STATUS_FAILED,
                'completed_at' => Carbon::now(),
                'response' => json_encode($response)
            ]);
        } else {
            $this->update([
                'queue_attempt' => $this->queue_attempt + 1,
                'status' => self::STATUS_PENDING,
                'response' => json_encode($response)
            ]);
        }
    }

    public static function SendForProcessing($handlerClass, $payloadDTO = [], $config = [], $groupName = null, $priority = null, $label = null): void
    {
        $tenant = tenant();
        if ($tenant) {
            // Create a new queue instance to use the setPayloadData method
            $queue = new self();
            $queue->setPayloadData($payloadDTO);

            $queueData = [
                'handler'       => $handlerClass,
                'group'         => $groupName,
                'config'        => $config,
                'label'         => $label,
                'priority'      => $priority,
                'queue_tries'   => GalaxyQueue::QUEUE_TRIES,
                'tenant_id'     => $tenant->id,
                'status'        => GalaxyQueue::STATUS_PENDING,
            ];

            // Add payload data based on whether blob is used
            if (!empty($queue->payload_blob)) {
                $queueData['payload_blob'] = $queue->payload_blob;
                $queueData['payload'] = $queue->payload;
            } else {
                $queueData['payload'] = $queue->payload;
            }

            GalaxyQueue::create($queueData);
        }
    }
}

